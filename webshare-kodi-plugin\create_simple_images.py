#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Create simple placeholder images for the plugin using basic Python
"""

def create_ppm_icon():
    """Create a simple PPM format icon that can be converted to PNG"""
    width, height = 256, 256

    # PPM header
    ppm_data = f"P3\n{width} {height}\n255\n"

    # Create blue background with white circle and play triangle
    for y in range(height):
        for x in range(width):
            # Distance from center
            center_x, center_y = width // 2, height // 2
            dist_from_center = ((x - center_x) ** 2 + (y - center_y) ** 2) ** 0.5

            # Blue background
            r, g, b = 30, 58, 138  # Blue color

            # White circle
            if 30 < dist_from_center < 90:
                r, g, b = 255, 255, 255  # White

                # Play triangle inside circle
                # Simple triangle check
                if (x > center_x - 30 and x < center_x + 30 and
                    y > center_y - 30 and y < center_y + 30):
                    # Triangle points (simplified)
                    if (x - (center_x - 20)) * 2 > abs(y - center_y):
                        r, g, b = 30, 58, 138  # Blue triangle

            # Text area "WS" at bottom
            if (y > height - 60 and y < height - 20 and
                x > center_x - 30 and x < center_x + 30):
                r, g, b = 255, 255, 255  # White text area

            ppm_data += f"{r} {g} {b} "
        ppm_data += "\n"

    # Save PPM file
    with open('icon.ppm', 'w') as f:
        f.write(ppm_data)

    print("✓ Created icon.ppm (convert to PNG with: convert icon.ppm icon.png)")

def create_simple_png_data():
    """Create a simple PNG using basic binary data"""
    # This creates a minimal PNG file with basic structure
    # Note: This is a very simplified approach

    import struct
    import zlib

    width, height = 256, 256

    # PNG signature
    png_signature = b'\x89PNG\r\n\x1a\n'

    # IHDR chunk
    ihdr_data = struct.pack('>IIBBBBB', width, height, 8, 2, 0, 0, 0)
    ihdr_crc = zlib.crc32(b'IHDR' + ihdr_data) & 0xffffffff
    ihdr_chunk = struct.pack('>I', len(ihdr_data)) + b'IHDR' + ihdr_data + struct.pack('>I', ihdr_crc)

    # Create simple image data (blue background)
    image_data = bytearray()
    for y in range(height):
        image_data.append(0)  # Filter type
        for x in range(width):
            # Simple blue pixel (RGB)
            image_data.extend([30, 58, 138])  # Blue color

    # Compress image data
    compressed_data = zlib.compress(bytes(image_data))

    # IDAT chunk
    idat_crc = zlib.crc32(b'IDAT' + compressed_data) & 0xffffffff
    idat_chunk = struct.pack('>I', len(compressed_data)) + b'IDAT' + compressed_data + struct.pack('>I', idat_crc)

    # IEND chunk
    iend_crc = zlib.crc32(b'IEND') & 0xffffffff
    iend_chunk = struct.pack('>I', 0) + b'IEND' + struct.pack('>I', iend_crc)

    # Combine all chunks
    png_data = png_signature + ihdr_chunk + idat_chunk + iend_chunk

    # Save PNG file
    with open('icon.png', 'wb') as f:
        f.write(png_data)

    print("✓ Created basic icon.png")

def create_html_templates():
    """Create HTML templates for manual screenshot creation"""

    # Icon template
    icon_html = '''<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            margin: 0;
            padding: 0;
            width: 256px;
            height: 256px;
            background: #1e3a8a;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-family: Arial, sans-serif;
        }
        .circle {
            width: 180px;
            height: 180px;
            background: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 10px;
        }
        .play {
            width: 0;
            height: 0;
            border-left: 40px solid #1e3a8a;
            border-top: 25px solid transparent;
            border-bottom: 25px solid transparent;
            margin-left: 10px;
        }
        .text {
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="circle">
        <div class="play"></div>
    </div>
    <div class="text">WS</div>
</body>
</html>'''

    # Fanart template
    fanart_html = '''<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            margin: 0;
            padding: 0;
            width: 1280px;
            height: 720px;
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-family: Arial, sans-serif;
            color: white;
        }
        .play-icon {
            width: 0;
            height: 0;
            border-left: 80px solid rgba(255,255,255,0.3);
            border-top: 50px solid transparent;
            border-bottom: 50px solid transparent;
            margin-bottom: 40px;
        }
        .title {
            font-size: 72px;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .subtitle {
            font-size: 36px;
            opacity: 0.9;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
    </style>
</head>
<body>
    <div class="play-icon"></div>
    <div class="title">Webshare.cz</div>
    <div class="subtitle">Stream movies and TV shows</div>
</body>
</html>'''

    # Save templates
    with open('icon_template.html', 'w') as f:
        f.write(icon_html)

    with open('fanart_template.html', 'w') as f:
        f.write(fanart_html)

    print("✓ Created icon_template.html (screenshot at 256x256)")
    print("✓ Created fanart_template.html (screenshot at 1280x720)")

def create_ascii_art_files():
    """Create text-based representations for documentation"""

    icon_ascii = '''
Webshare.cz Kodi Plugin Icon (256x256)
=====================================

    ################################
    ##                            ##
    ##        ############        ##
    ##      ##            ##      ##
    ##    ##                ##    ##
    ##  ##      ########      ##  ##
    ## ##      ##      ##      ## ##
    ####      ##        ##      ####
    ## ##    ##          ##    ## ##
    ##  ##  ##            ##  ##  ##
    ##    ####            ####    ##
    ##      ##            ##      ##
    ##        ############        ##
    ##                            ##
    ##             WS             ##
    ################################

Blue background (#1e3a8a)
White circle with blue play triangle
"WS" text at bottom
'''

    fanart_ascii = '''
Webshare.cz Kodi Plugin Fanart (1280x720)
=========================================

################################################################################
##                                                                            ##
##                                                                            ##
##                                                                            ##
##                                    >                                       ##
##                                                                            ##
##                              Webshare.cz                                   ##
##                                                                            ##
##                        Stream movies and TV shows                          ##
##                                                                            ##
##                                                                            ##
##                                                                            ##
################################################################################

Blue gradient background
Semi-transparent play icon
White text with shadow
'''

    with open('ICON_DESIGN.txt', 'w', encoding='utf-8') as f:
        f.write(icon_ascii)

    with open('FANART_DESIGN.txt', 'w', encoding='utf-8') as f:
        f.write(fanart_ascii)

    print("✓ Created ICON_DESIGN.txt")
    print("✓ Created FANART_DESIGN.txt")

def main():
    """Create all image templates and placeholders"""
    print("Creating image templates for Webshare Kodi Plugin")
    print("=" * 50)

    # Create different formats
    try:
        create_simple_png_data()
    except Exception as e:
        print(f"⚠ Could not create PNG directly: {e}")

    create_ppm_icon()
    create_html_templates()
    create_ascii_art_files()

    print("\n" + "=" * 50)
    print("Image templates created!")
    print("\nTo create final images:")
    print("1. Open icon_template.html in browser, screenshot at 256x256 → icon.png")
    print("2. Open fanart_template.html in browser, screenshot at 1280x720 → fanart.jpg")
    print("3. Or convert icon.ppm to PNG using ImageMagick: convert icon.ppm icon.png")

if __name__ == '__main__':
    main()
