<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<addon id="plugin.video.webshare"
       name="Webshare.cz"
       version="1.0.1"
       provider-name="Webshare Plugin">
    <requires>
        <import addon="xbmc.python" version="3.0.0"/>
        <import addon="script.module.requests" version="2.25.1"/>
    </requires>
    <extension point="xbmc.python.pluginsource" library="addon.py">
        <provides>video</provides>
    </extension>
    <extension point="xbmc.addon.metadata">
        <summary lang="en_GB">Webshare.cz video plugin</summary>
        <summary lang="cs_CZ">Webshare.cz video plugin</summary>
        <description lang="en_GB">Stream videos from Webshare.cz service</description>
        <description lang="cs_CZ">Přehrávání videí ze služby Webshare.cz</description>
        <platform>all</platform>
        <license>MIT</license>
        <website>https://webshare.cz</website>
        <assets>
            <icon>icon.png</icon>
            <fanart>fanart.jpg</fanart>
        </assets>
    </extension>
</addon>
