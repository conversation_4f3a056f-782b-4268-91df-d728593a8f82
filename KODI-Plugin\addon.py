# -*- coding: utf-8 -*-

import sys
import urllib.parse
import xbmc
import xbmcgui
import xbmcplugin
import xbmcaddon
import requests
import json

# Get addon handle and base URL
addon_handle = int(sys.argv[1])
base_url = sys.argv[0]
addon = xbmcaddon.Addon()

def log(message):
    """Simple logging function"""
    xbmc.log(f"[Webshare Plugin] {message}", xbmc.LOGINFO)

def build_url(query):
    """Build plugin URL"""
    return base_url + '?' + urllib.parse.urlencode(query)

def get_credentials():
    """Get username and password from settings"""
    username = addon.getSetting('username')
    password = addon.getSetting('password')
    return username, password

def show_settings():
    """Open addon settings"""
    addon.openSettings()

def show_message(title, message):
    """Show message dialog"""
    xbmcgui.Dialog().ok(title, message)

def webshare_login():
    """Login to Webshare API"""
    username, password = get_credentials()
    if not username or not password:
        return None

    try:
        # Try new API endpoint first
        url = "https://webshare.cz/api/login/"
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Kodi Webshare Plugin'
        }
        data = {
            'username': username,
            'password': password
        }

        log(f"Attempting login for user: {username}")
        response = requests.post(url, data=data, headers=headers, timeout=15)
        log(f"Login response status: {response.status_code}")
        log(f"Login response content: {response.text[:200]}")

        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('status') == 'OK':
                    log("Login successful")
                    return result.get('token')
                else:
                    log(f"Login failed: {result.get('message', 'Unknown error')}")
            except ValueError as e:
                log(f"JSON decode error: {e}")
                log(f"Response was: {response.text}")
        else:
            log(f"HTTP error: {response.status_code}")

        return None
    except Exception as e:
        log(f"Login error: {e}")
        return None

def get_popular_files(token):
    """Get popular files from Webshare"""
    try:
        url = "https://webshare.cz/api/search/"
        headers = {'Authorization': f'Token {token}'}
        params = {
            'what': '',
            'sort': 'largest',
            'limit': 20
        }
        response = requests.get(url, headers=headers, params=params, timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'OK':
                return result.get('file_list', [])
        return []
    except Exception as e:
        log(f"Search error: {e}")
        return []

def search_files(token, query):
    """Search files on Webshare"""
    try:
        url = "https://webshare.cz/api/search/"
        headers = {'Authorization': f'Token {token}'}
        params = {
            'what': query,
            'sort': 'largest',
            'limit': 50
        }
        response = requests.get(url, headers=headers, params=params, timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'OK':
                return result.get('file_list', [])
        return []
    except Exception as e:
        log(f"Search error: {e}")
        return []

def is_video_file(filename):
    """Check if file is video"""
    video_extensions = ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v']
    return any(filename.lower().endswith(ext) for ext in video_extensions)

def format_file_size(size_bytes):
    """Format file size"""
    try:
        size = int(size_bytes)
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
    except:
        return "Unknown"

def main_menu():
    """Show main menu"""
    log("Showing main menu")

    # Check if credentials are set
    username, password = get_credentials()
    if not username or not password:
        show_message("Webshare.cz", "Please configure your login credentials in settings")
        show_settings()
        return

    # Try to login
    token = webshare_login()
    if not token:
        show_message("Login Failed", "Cannot login to Webshare.cz. Check your credentials.")
        show_settings()
        return

    log("Login successful, showing popular files")

    # Get popular files
    files = get_popular_files(token)
    video_files = [f for f in files if is_video_file(f.get('name', ''))]

    # Add search option
    url = build_url({'action': 'search', 'token': token})
    li = xbmcgui.ListItem('🔍 Search Movies & TV Shows')
    xbmcplugin.addDirectoryItem(handle=addon_handle, url=url, listitem=li, isFolder=True)

    # Add popular files
    for file_info in video_files[:15]:  # Show top 15
        name = file_info.get('name', 'Unknown')
        size = format_file_size(file_info.get('size', 0))
        ident = file_info.get('ident')

        if ident:
            title = f"🎬 {name} ({size})"
            url = build_url({'action': 'play', 'ident': ident, 'token': token})
            li = xbmcgui.ListItem(title)
            li.setInfo('video', {'title': name, 'mediatype': 'movie'})
            xbmcplugin.addDirectoryItem(handle=addon_handle, url=url, listitem=li, isFolder=False)

    # Add settings
    url = build_url({'action': 'settings'})
    li = xbmcgui.ListItem('⚙️ Settings')
    xbmcplugin.addDirectoryItem(handle=addon_handle, url=url, listitem=li, isFolder=False)

    xbmcplugin.setContent(addon_handle, 'movies')
    xbmcplugin.endOfDirectory(addon_handle)

def search():
    """Search function"""
    log("Search function called")

    # Get token from params
    params = dict(urllib.parse.parse_qsl(sys.argv[2][1:]))
    token = params.get('token')

    if not token:
        show_message("Error", "Session expired. Please restart plugin.")
        return

    keyboard = xbmc.Keyboard('', 'Enter search term')
    keyboard.doModal()

    if keyboard.isConfirmed():
        query = keyboard.getText()
        if query:
            log(f"Searching for: {query}")
            files = search_files(token, query)
            video_files = [f for f in files if is_video_file(f.get('name', ''))]

            if not video_files:
                show_message("No Results", f"No videos found for '{query}'")
                return

            # Show search results
            for file_info in video_files:
                name = file_info.get('name', 'Unknown')
                size = format_file_size(file_info.get('size', 0))
                ident = file_info.get('ident')

                if ident:
                    title = f"🎬 {name} ({size})"
                    url = build_url({'action': 'play', 'ident': ident, 'token': token})
                    li = xbmcgui.ListItem(title)
                    li.setInfo('video', {'title': name, 'mediatype': 'movie'})
                    xbmcplugin.addDirectoryItem(handle=addon_handle, url=url, listitem=li, isFolder=False)

            xbmcplugin.setContent(addon_handle, 'movies')
            xbmcplugin.endOfDirectory(addon_handle)

def get_download_link(token, ident):
    """Get download link for file"""
    try:
        url = "https://webshare.cz/api/file_link/"
        headers = {'Authorization': f'Token {token}'}
        data = {'ident': ident}
        response = requests.post(url, headers=headers, data=data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'OK':
                return result.get('link')
        return None
    except Exception as e:
        log(f"Download link error: {e}")
        return None

def play_file():
    """Play video file"""
    params = dict(urllib.parse.parse_qsl(sys.argv[2][1:]))
    token = params.get('token')
    ident = params.get('ident')

    if not token or not ident:
        show_message("Error", "Missing parameters")
        return

    log(f"Playing file: {ident}")

    # Get download link
    download_url = get_download_link(token, ident)
    if not download_url:
        show_message("Error", "Cannot get download link")
        return

    # Play the file
    li = xbmcgui.ListItem(path=download_url)
    xbmcplugin.setResolvedUrl(addon_handle, True, li)

def router(paramstring):
    """Route to appropriate function"""
    params = dict(urllib.parse.parse_qsl(paramstring))

    if params:
        action = params.get('action')
        if action == 'search':
            search()
        elif action == 'play':
            play_file()
        elif action == 'settings':
            show_settings()
        else:
            main_menu()
    else:
        main_menu()

if __name__ == '__main__':
    router(sys.argv[2][1:])
