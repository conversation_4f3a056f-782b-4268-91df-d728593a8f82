# -*- coding: utf-8 -*-

import requests
import hashlib
import xml.etree.ElementTree as ET
from urllib.parse import urlencode
import xbmc
import xbmcaddon

class WebshareAPI:
    """Webshare.cz API wrapper"""
    
    def __init__(self):
        self.addon = xbmcaddon.Addon()
        self.base_url = "https://webshare.cz/api"
        self.session_token = None
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Kodi Webshare Plugin/1.0.0',
            'Accept': 'text/xml; charset=UTF-8',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        })
    
    def _log(self, message, level=xbmc.LOGDEBUG):
        """Log message if debug logging is enabled"""
        if self.addon.getSettingBool('debug_logging'):
            xbmc.log(f"[Webshare API] {message}", level)
    
    def _md5_crypt(self, password):
        """Create MD5 hash for password"""
        return hashlib.md5(password.encode('utf-8')).hexdigest()
    
    def _sha1_md5_crypt(self, password):
        """Create SHA1(MD5_CRYPT(password)) hash for file passwords"""
        md5_hash = self._md5_crypt(password)
        return hashlib.sha1(md5_hash.encode('utf-8')).hexdigest()
    
    def _make_request(self, endpoint, data=None):
        """Make API request and parse XML response"""
        url = f"{self.base_url}/{endpoint}/"
        
        try:
            if data:
                response = self.session.post(url, data=urlencode(data))
            else:
                response = self.session.post(url)
            
            response.raise_for_status()
            
            # Parse XML response
            root = ET.fromstring(response.content)
            
            # Check for errors
            status = root.find('status')
            if status is not None and status.text == 'FATAL':
                error_code = root.find('code')
                error_message = root.find('message')
                error_text = error_message.text if error_message is not None else 'Unknown error'
                self._log(f"API Error: {error_text}", xbmc.LOGERROR)
                return None
            
            return root
            
        except requests.RequestException as e:
            self._log(f"Request error: {str(e)}", xbmc.LOGERROR)
            return None
        except ET.ParseError as e:
            self._log(f"XML parse error: {str(e)}", xbmc.LOGERROR)
            return None
    
    def login(self, username, password):
        """Login to Webshare.cz"""
        self._log(f"Attempting login for user: {username}")
        
        # Get salt first
        salt_response = self._make_request('salt', {'username_or_email': username})
        if not salt_response:
            return False
        
        salt_element = salt_response.find('salt')
        if salt_element is None:
            self._log("No salt received", xbmc.LOGERROR)
            return False
        
        salt = salt_element.text
        
        # Create password hash with salt
        password_hash = hashlib.sha1((self._md5_crypt(password) + salt).encode('utf-8')).hexdigest()
        
        # Login request
        login_data = {
            'username_or_email': username,
            'password': password_hash,
            'keep_logged_in': 1
        }
        
        response = self._make_request('login', login_data)
        if not response:
            return False
        
        token_element = response.find('token')
        if token_element is not None:
            self.session_token = token_element.text
            self.session.headers['wst'] = self.session_token
            self._log("Login successful")
            return True
        
        self._log("Login failed - no token received", xbmc.LOGERROR)
        return False
    
    def search(self, query, category=None, sort='recent', limit=50, offset=0):
        """Search for files"""
        if not self.session_token:
            return None
        
        search_data = {
            'what': query,
            'sort': sort,
            'limit': limit,
            'offset': offset
        }
        
        if category:
            search_data['category'] = category
        
        response = self._make_request('search', search_data)
        if not response:
            return None
        
        files = []
        for file_elem in response.findall('file'):
            file_info = self._parse_file_element(file_elem)
            if file_info:
                files.append(file_info)
        
        return files
    
    def get_file_info(self, file_ident, password=None):
        """Get detailed file information"""
        if not self.session_token:
            return None
        
        data = {'ident': file_ident}
        if password:
            data['password'] = self._sha1_md5_crypt(password)
        
        response = self._make_request('file_info', data)
        if not response:
            return None
        
        return self._parse_file_info(response)
    
    def get_download_link(self, file_ident, password=None):
        """Get direct download link for file"""
        if not self.session_token:
            return None
        
        data = {'ident': file_ident}
        if password:
            data['password'] = self._sha1_md5_crypt(password)
        
        response = self._make_request('file_link', data)
        if not response:
            return None
        
        link_element = response.find('link')
        if link_element is not None:
            return link_element.text
        
        return None
    
    def _parse_file_element(self, file_elem):
        """Parse file element from XML"""
        file_info = {}
        
        for child in file_elem:
            if child.tag in ['ident', 'name', 'type', 'size']:
                file_info[child.tag] = child.text
            elif child.tag in ['password', 'adult', 'copyrighted']:
                file_info[child.tag] = child.text == '1'
        
        return file_info
    
    def _parse_file_info(self, response):
        """Parse detailed file info from XML response"""
        file_info = {}
        
        for child in response:
            if child.tag in ['name', 'description', 'size', 'type']:
                file_info[child.tag] = child.text
            elif child.tag in ['password', 'adult', 'copyrighted', 'available', 'removed']:
                file_info[child.tag] = child.text == '1'
            elif child.tag in ['positive_votes', 'negative_votes']:
                file_info[child.tag] = int(child.text) if child.text else 0
        
        return file_info
