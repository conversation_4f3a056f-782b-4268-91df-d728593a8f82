#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for Webshare API functionality
Run this to test the API without Kodi
"""

import sys
import os

# Add lib path
sys.path.append(os.path.join(os.path.dirname(__file__), 'resources', 'lib'))

# Mock <PERSON>di modules for testing outside Kodi
class MockXBMC:
    LOGDEBUG = 0
    LOGINFO = 1
    LOGWARNING = 2
    LOGERROR = 3

    @staticmethod
    def log(msg, level=0):
        print(f"[KODI LOG] {msg}")

class MockXBMCAddon:
    def __init__(self):
        self.settings = {}

    def getSettingBool(self, key):
        return self.settings.get(key, False)

    def getSetting(self, key):
        return self.settings.get(key, "")

# Install mock modules
sys.modules['xbmc'] = MockXBMC()
sys.modules['xbmcaddon'] = type('MockModule', (), {'Addon': MockXBMCAddon})()

try:
    from webshare_api import WebshareAPI
    import requests
except ImportError as e:
    print(f"Missing dependencies: {e}")
    print("Install with: pip install requests")
    sys.exit(1)

def test_api_connection():
    """Test basic API connection"""
    print("Testing Webshare.cz API connection...")

    try:
        response = requests.get("https://webshare.cz", timeout=10)
        if response.status_code == 200:
            print("✓ Webshare.cz is accessible")
            return True
        else:
            print(f"✗ Webshare.cz returned status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Cannot connect to Webshare.cz: {e}")
        return False

def test_api_endpoints():
    """Test API endpoints without authentication"""
    print("\nTesting API endpoints...")

    api = WebshareAPI()

    # Test salt endpoint (doesn't require auth)
    try:
        response = api._make_request('salt', {'username_or_email': '<EMAIL>'})
        if response is not None:
            print("✓ API endpoints are accessible")
            return True
        else:
            print("✗ API endpoints not accessible")
            return False
    except Exception as e:
        print(f"✗ API test failed: {e}")
        return False

def test_login():
    """Test login functionality"""
    print("\nTesting login functionality...")
    print("Note: This requires valid Webshare.cz credentials")

    username = input("Enter username (or press Enter to skip): ").strip()
    if not username:
        print("⚠ Skipping login test")
        return True

    import getpass
    password = getpass.getpass("Enter password: ")

    api = WebshareAPI()

    try:
        if api.login(username, password):
            print("✓ Login successful!")

            # Test search
            print("Testing search...")
            results = api.search("test", limit=5)
            if results:
                print(f"✓ Search returned {len(results)} results")
                for i, result in enumerate(results[:3]):
                    print(f"  {i+1}. {result.get('name', 'Unknown')}")
            else:
                print("⚠ Search returned no results")

            return True
        else:
            print("✗ Login failed")
            return False
    except Exception as e:
        print(f"✗ Login test failed: {e}")
        return False

def test_file_detection():
    """Test video file detection"""
    print("\nTesting file detection...")

    test_files = [
        "Movie.2023.1080p.BluRay.x264.mp4",
        "TV.Show.S01E01.720p.HDTV.mkv",
        "Document.pdf",
        "Music.mp3",
        "Series.Season.1.Episode.1.avi"
    ]

    # Import utils (mock xbmc modules first)
    class MockXBMC:
        LOGDEBUG = 0
        LOGINFO = 1
        LOGWARNING = 2
        LOGERROR = 3

        @staticmethod
        def log(msg, level=0):
            pass

    class MockXBMCAddon:
        def __init__(self):
            pass
        def getSettingBool(self, key):
            return False

    sys.modules['xbmc'] = MockXBMC()
    sys.modules['xbmcaddon'] = type('MockModule', (), {'Addon': MockXBMCAddon})()

    try:
        import utils

        for filename in test_files:
            is_video = utils.is_video_file(filename)
            video_info = utils.get_video_info(filename)
            print(f"  {filename}")
            print(f"    Video: {is_video}")
            print(f"    Type: {video_info.get('mediatype', 'unknown')}")
            if 'year' in video_info and video_info['year']:
                print(f"    Year: {video_info['year']}")
            print()

        print("✓ File detection working")
        return True

    except Exception as e:
        print(f"✗ File detection test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Webshare.cz Kodi Plugin - API Test")
    print("=" * 40)

    tests = [
        ("API Connection", test_api_connection),
        ("API Endpoints", test_api_endpoints),
        ("File Detection", test_file_detection),
        ("Login (Optional)", test_login),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n[{test_name}]")
        try:
            result = test_func()
            results.append((test_name, result))
        except KeyboardInterrupt:
            print("\n⚠ Test interrupted by user")
            results.append((test_name, False))
            break
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 40)
    print("TEST SUMMARY")
    print("=" * 40)

    passed = 0
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\nPassed: {passed}/{len(results)}")

    if passed == len(results):
        print("🎉 All tests passed! Plugin should work correctly.")
    elif passed >= len(results) - 1:  # Allow login test to fail
        print("✅ Core functionality working. Plugin ready for use.")
    else:
        print("⚠ Some tests failed. Check your setup.")

    print("\nTo install the plugin:")
    print("1. Use the ZIP file: plugin.video.webshare-1.0.0.zip")
    print("2. Install in Kodi via Add-ons → Install from zip file")
    print("3. Configure your Webshare.cz credentials in plugin settings")

if __name__ == '__main__':
    main()
