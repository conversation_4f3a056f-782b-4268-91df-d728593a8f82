#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Build script for Webshare Kodi plugin
Creates installable ZIP package
"""

import os
import zipfile
import shutil
from datetime import datetime

def create_simple_icon():
    """Create a simple text-based icon if PIL is not available"""
    # Create a simple SVG icon and convert to PNG using basic method
    svg_content = '''<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" xmlns="http://www.w3.org/2000/svg">
  <rect width="256" height="256" fill="#1e3a8a"/>
  <circle cx="128" cy="128" r="90" fill="white" stroke="#1e3a8a" stroke-width="4"/>
  <polygon points="98,88 98,168 178,128" fill="#1e3a8a"/>
  <text x="128" y="220" text-anchor="middle" fill="white" font-family="Arial" font-size="24" font-weight="bold">WS</text>
</svg>'''
    
    with open('icon.svg', 'w') as f:
        f.write(svg_content)
    
    # Create a simple placeholder PNG (will need manual conversion)
    print("SVG icon created. Please convert icon.svg to icon.png (256x256) manually.")

def create_simple_fanart():
    """Create a simple fanart placeholder"""
    # Create simple HTML that can be screenshot for fanart
    html_content = '''<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            margin: 0;
            padding: 0;
            width: 1280px;
            height: 720px;
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-family: Arial, sans-serif;
            color: white;
        }
        .play-icon {
            width: 0;
            height: 0;
            border-left: 60px solid white;
            border-top: 40px solid transparent;
            border-bottom: 40px solid transparent;
            margin-bottom: 40px;
            opacity: 0.7;
        }
        .title {
            font-size: 72px;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .subtitle {
            font-size: 36px;
            opacity: 0.9;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
    </style>
</head>
<body>
    <div class="play-icon"></div>
    <div class="title">Webshare.cz</div>
    <div class="subtitle">Stream movies and TV shows</div>
</body>
</html>'''
    
    with open('fanart.html', 'w') as f:
        f.write(html_content)
    
    print("Fanart HTML created. Please take a 1280x720 screenshot and save as fanart.jpg.")

def create_package():
    """Create ZIP package for Kodi installation"""
    
    # Plugin info
    plugin_id = "plugin.video.webshare"
    version = "1.0.0"
    
    # Files to include in package
    files_to_include = [
        'addon.xml',
        'addon.py',
        'LICENSE.txt',
        'README.md',
        'resources/settings.xml',
        'resources/lib/webshare_api.py',
        'resources/lib/utils.py',
        'resources/language/resource.language.en_gb/strings.po',
        'resources/language/resource.language.cs_cz/strings.po'
    ]
    
    # Optional files (include if they exist)
    optional_files = [
        'icon.png',
        'fanart.jpg'
    ]
    
    # Create package directory
    package_dir = f"{plugin_id}-{version}"
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)
    
    # Copy files to package directory
    print(f"Creating package directory: {package_dir}")
    
    for file_path in files_to_include:
        src_path = file_path
        dst_path = os.path.join(package_dir, file_path)
        
        # Create directory if needed
        dst_dir = os.path.dirname(dst_path)
        if dst_dir and not os.path.exists(dst_dir):
            os.makedirs(dst_dir)
        
        if os.path.exists(src_path):
            shutil.copy2(src_path, dst_path)
            print(f"✓ Copied {file_path}")
        else:
            print(f"✗ Missing {file_path}")
    
    # Copy optional files
    for file_path in optional_files:
        if os.path.exists(file_path):
            dst_path = os.path.join(package_dir, file_path)
            shutil.copy2(file_path, dst_path)
            print(f"✓ Copied {file_path}")
        else:
            print(f"⚠ Optional file missing: {file_path}")
    
    # Create ZIP package
    zip_filename = f"{plugin_id}-{version}.zip"
    print(f"\nCreating ZIP package: {zip_filename}")
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_path = os.path.relpath(file_path, package_dir)
                zipf.write(file_path, f"{plugin_id}/{arc_path}")
                print(f"  Added: {arc_path}")
    
    print(f"\n✓ Package created: {zip_filename}")
    print(f"✓ Package directory: {package_dir}")
    
    return zip_filename, package_dir

def create_installation_guide():
    """Create installation guide"""
    guide = f"""# Webshare.cz Kodi Plugin - Installation Guide

Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Quick Installation

1. Download the ZIP file: `plugin.video.webshare-1.0.0.zip`
2. In Kodi, go to **Add-ons** → **Install from zip file**
3. Select the downloaded ZIP file
4. The plugin will be installed automatically

## Manual Installation

1. Extract the ZIP file
2. Copy the `plugin.video.webshare` folder to:
   - **Windows**: `%APPDATA%\\Kodi\\addons\\`
   - **Linux**: `~/.kodi/addons/`
   - **macOS**: `~/Library/Application Support/Kodi/addons/`
   - **Android**: `/storage/emulated/0/Android/data/org.xbmc.kodi/files/.kodi/addons/`
3. Restart Kodi
4. The plugin will appear in **Add-ons** → **Video add-ons**

## Configuration

1. Go to **Add-ons** → **Video add-ons** → **Webshare.cz**
2. Right-click and select **Settings**
3. Enter your Webshare.cz credentials:
   - **Username**: Your Webshare.cz username or email
   - **Password**: Your Webshare.cz password

## First Use

1. Open the plugin from **Add-ons** → **Video add-ons** → **Webshare.cz**
2. If not configured, you'll be prompted to enter credentials
3. Use **Search** to find movies and TV shows
4. Click on any video file to start streaming

## Troubleshooting

- **Plugin doesn't appear**: Check that all files are in the correct location
- **Login fails**: Verify your Webshare.cz credentials
- **Videos don't play**: Check your internet connection and file availability

## Support

For issues and support, please check the README.md file included with the plugin.
"""
    
    with open('INSTALLATION_GUIDE.txt', 'w', encoding='utf-8') as f:
        f.write(guide)
    
    print("✓ Installation guide created: INSTALLATION_GUIDE.txt")

def main():
    """Main build process"""
    print("Building Webshare Kodi Plugin Package")
    print("=" * 40)
    
    # Check for required files
    required_files = ['addon.xml', 'addon.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"✗ Missing required files: {', '.join(missing_files)}")
        return
    
    # Create icons if they don't exist
    if not os.path.exists('icon.png'):
        print("Creating placeholder icon...")
        create_simple_icon()
    
    if not os.path.exists('fanart.jpg'):
        print("Creating placeholder fanart...")
        create_simple_fanart()
    
    # Create package
    zip_file, package_dir = create_package()
    
    # Create installation guide
    create_installation_guide()
    
    print("\n" + "=" * 40)
    print("Build completed successfully!")
    print(f"📦 Package: {zip_file}")
    print(f"📁 Directory: {package_dir}")
    print("📋 Installation guide: INSTALLATION_GUIDE.txt")
    print("\nReady for installation in Kodi!")

if __name__ == '__main__':
    main()
