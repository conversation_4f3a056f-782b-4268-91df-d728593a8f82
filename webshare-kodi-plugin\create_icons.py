#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
<PERSON><PERSON><PERSON> to create simple icons for the Webshare Kodi plugin
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon():
    """Create plugin icon (256x256)"""
    # Create image with blue background
    img = Image.new('RGB', (256, 256), color='#1e3a8a')
    draw = ImageDraw.Draw(img)
    
    # Draw white circle
    circle_margin = 30
    draw.ellipse([circle_margin, circle_margin, 256-circle_margin, 256-circle_margin], 
                 fill='white', outline='#1e3a8a', width=4)
    
    # Draw play triangle
    triangle_size = 60
    center_x, center_y = 128, 128
    triangle_points = [
        (center_x - triangle_size//2, center_y - triangle_size//2),
        (center_x - triangle_size//2, center_y + triangle_size//2),
        (center_x + triangle_size//2, center_y)
    ]
    draw.polygon(triangle_points, fill='#1e3a8a')
    
    # Add text "WS" below
    try:
        font = ImageFont.truetype("arial.ttf", 36)
    except:
        font = ImageFont.load_default()
    
    text = "WS"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    text_x = (256 - text_width) // 2
    text_y = 180
    
    draw.text((text_x, text_y), text, fill='white', font=font)
    
    return img

def create_fanart():
    """Create fanart background (1280x720)"""
    # Create gradient background
    img = Image.new('RGB', (1280, 720), color='#1e3a8a')
    draw = ImageDraw.Draw(img)
    
    # Create gradient effect
    for y in range(720):
        color_value = int(30 + (y / 720) * 60)  # Gradient from dark to lighter blue
        color = (color_value//3, color_value//2, color_value)
        draw.line([(0, y), (1280, y)], fill=color)
    
    # Add large play icon in center
    center_x, center_y = 640, 360
    triangle_size = 120
    triangle_points = [
        (center_x - triangle_size//2, center_y - triangle_size//2),
        (center_x - triangle_size//2, center_y + triangle_size//2),
        (center_x + triangle_size//2, center_y)
    ]
    
    # Draw semi-transparent white triangle
    overlay = Image.new('RGBA', (1280, 720), (255, 255, 255, 0))
    overlay_draw = ImageDraw.Draw(overlay)
    overlay_draw.polygon(triangle_points, fill=(255, 255, 255, 100))
    
    img = Image.alpha_composite(img.convert('RGBA'), overlay).convert('RGB')
    
    # Add text
    draw = ImageDraw.Draw(img)
    try:
        font_large = ImageFont.truetype("arial.ttf", 72)
        font_small = ImageFont.truetype("arial.ttf", 36)
    except:
        font_large = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # Main title
    title = "Webshare.cz"
    bbox = draw.textbbox((0, 0), title, font=font_large)
    title_width = bbox[2] - bbox[0]
    title_x = (1280 - title_width) // 2
    title_y = 480
    
    # Add shadow
    draw.text((title_x + 2, title_y + 2), title, fill=(0, 0, 0, 128), font=font_large)
    draw.text((title_x, title_y), title, fill='white', font=font_large)
    
    # Subtitle
    subtitle = "Stream movies and TV shows"
    bbox = draw.textbbox((0, 0), subtitle, font=font_small)
    subtitle_width = bbox[2] - bbox[0]
    subtitle_x = (1280 - subtitle_width) // 2
    subtitle_y = 560
    
    draw.text((subtitle_x + 1, subtitle_y + 1), subtitle, fill=(0, 0, 0, 128), font=font_small)
    draw.text((subtitle_x, subtitle_y), subtitle, fill='white', font=font_small)
    
    return img

def main():
    """Create and save icons"""
    print("Creating Webshare Kodi plugin icons...")
    
    # Create icon
    print("Creating icon.png...")
    icon = create_icon()
    icon.save('icon.png', 'PNG')
    print("✓ icon.png created")
    
    # Create fanart
    print("Creating fanart.jpg...")
    fanart = create_fanart()
    fanart.save('fanart.jpg', 'JPEG', quality=85)
    print("✓ fanart.jpg created")
    
    print("Icons created successfully!")

if __name__ == '__main__':
    main()
