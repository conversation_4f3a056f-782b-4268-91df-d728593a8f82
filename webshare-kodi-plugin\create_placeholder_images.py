#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Create placeholder images for the plugin using basic methods
"""

def create_simple_icon_png():
    """Create a very basic PNG icon"""
    import struct
    import zlib
    
    width, height = 256, 256
    
    # PNG signature
    png_data = b'\x89PNG\r\n\x1a\n'
    
    # IHDR chunk
    ihdr_data = struct.pack('>IIBBBBB', width, height, 8, 2, 0, 0, 0)
    ihdr_crc = zlib.crc32(b'IHDR' + ihdr_data) & 0xffffffff
    ihdr_chunk = struct.pack('>I', len(ihdr_data)) + b'IHDR' + ihdr_data + struct.pack('>I', ihdr_crc)
    
    # Create image data
    image_data = bytearray()
    for y in range(height):
        image_data.append(0)  # Filter type
        for x in range(width):
            # Create a simple design
            center_x, center_y = width // 2, height // 2
            dist = ((x - center_x) ** 2 + (y - center_y) ** 2) ** 0.5
            
            if dist < 90:  # White circle
                if (x > center_x - 30 and x < center_x + 20 and 
                    abs(y - center_y) < 25 and 
                    (x - center_x + 30) * 1.5 > abs(y - center_y)):  # Triangle
                    image_data.extend([30, 58, 138])  # Blue triangle
                else:
                    image_data.extend([255, 255, 255])  # White circle
            else:
                image_data.extend([30, 58, 138])  # Blue background
    
    # Compress image data
    compressed_data = zlib.compress(bytes(image_data))
    
    # IDAT chunk
    idat_crc = zlib.crc32(b'IDAT' + compressed_data) & 0xffffffff
    idat_chunk = struct.pack('>I', len(compressed_data)) + b'IDAT' + compressed_data + struct.pack('>I', idat_crc)
    
    # IEND chunk
    iend_crc = zlib.crc32(b'IEND') & 0xffffffff
    iend_chunk = struct.pack('>I', 0) + b'IEND' + struct.pack('>I', iend_crc)
    
    # Combine all chunks
    png_data += ihdr_chunk + idat_chunk + iend_chunk
    
    with open('icon.png', 'wb') as f:
        f.write(png_data)
    
    print("✓ Created icon.png")

def create_simple_fanart_jpg():
    """Create a simple JPEG fanart using basic method"""
    # Create a simple PPM and note for conversion
    width, height = 1280, 720
    
    ppm_data = f"P3\n{width} {height}\n255\n"
    
    for y in range(height):
        for x in range(width):
            # Create gradient background
            blue_intensity = int(30 + (y / height) * 60)
            r = blue_intensity // 3
            g = blue_intensity // 2  
            b = blue_intensity
            
            # Add some variation
            if x % 4 == 0 and y % 4 == 0:
                r += 10
                g += 10
                b += 10
            
            ppm_data += f"{r} {g} {b} "
        ppm_data += "\n"
    
    with open('fanart.ppm', 'w') as f:
        f.write(ppm_data)
    
    # Create a minimal JPEG-like file (actually just copy the PNG method for now)
    # This is a placeholder - in real use, convert the PPM to JPEG
    try:
        import struct
        import zlib
        
        # Create a simple blue gradient as PNG first, then rename to JPG
        png_data = b'\x89PNG\r\n\x1a\n'
        
        # IHDR chunk
        ihdr_data = struct.pack('>IIBBBBB', width, height, 8, 2, 0, 0, 0)
        ihdr_crc = zlib.crc32(b'IHDR' + ihdr_data) & 0xffffffff
        ihdr_chunk = struct.pack('>I', len(ihdr_data)) + b'IHDR' + ihdr_data + struct.pack('>I', ihdr_crc)
        
        # Create simple gradient image data
        image_data = bytearray()
        for y in range(height):
            image_data.append(0)  # Filter type
            for x in range(width):
                blue_intensity = int(30 + (y / height) * 60)
                r = blue_intensity // 3
                g = blue_intensity // 2
                b = blue_intensity
                image_data.extend([r, g, b])
        
        # Compress image data
        compressed_data = zlib.compress(bytes(image_data))
        
        # IDAT chunk
        idat_crc = zlib.crc32(b'IDAT' + compressed_data) & 0xffffffff
        idat_chunk = struct.pack('>I', len(compressed_data)) + b'IDAT' + compressed_data + struct.pack('>I', idat_crc)
        
        # IEND chunk
        iend_crc = zlib.crc32(b'IEND') & 0xffffffff
        iend_chunk = struct.pack('>I', 0) + b'IEND' + struct.pack('>I', iend_crc)
        
        # Combine all chunks
        png_data += ihdr_chunk + idat_chunk + iend_chunk
        
        # Save as PNG first, then copy to JPG (placeholder)
        with open('fanart_temp.png', 'wb') as f:
            f.write(png_data)
        
        # For now, just copy PNG to JPG (not ideal but works as placeholder)
        import shutil
        shutil.copy('fanart_temp.png', 'fanart.jpg')
        
        print("✓ Created fanart.jpg (placeholder)")
        
    except Exception as e:
        print(f"⚠ Could not create fanart.jpg: {e}")
        print("✓ Created fanart.ppm (convert with: convert fanart.ppm fanart.jpg)")

def main():
    """Create placeholder images"""
    print("Creating placeholder images...")
    
    try:
        create_simple_icon_png()
    except Exception as e:
        print(f"⚠ Could not create icon.png: {e}")
    
    try:
        create_simple_fanart_jpg()
    except Exception as e:
        print(f"⚠ Could not create fanart.jpg: {e}")
    
    print("Placeholder images created!")

if __name__ == '__main__':
    main()
