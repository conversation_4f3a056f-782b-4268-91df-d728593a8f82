# -*- coding: utf-8 -*-

import sys
import xbmc
import xbmcgui
import xbmcplugin
import xbmcaddon
from urllib.parse import urlencode, parse_qsl
import os

# Add resources/lib to path
addon_path = xbmcaddon.Addon().getAddonInfo('path')
lib_path = os.path.join(addon_path, 'resources', 'lib')
sys.path.append(lib_path)

from webshare_api import WebshareAPI
import utils

class WebsharePlugin:
    def __init__(self):
        self.addon = xbmcaddon.Addon()
        self.addon_handle = int(sys.argv[1])
        self.addon_url = sys.argv[0]
        self.api = WebshareAPI()
        
        # Parse parameters
        self.params = dict(parse_qsl(sys.argv[2][1:]))
        
    def build_url(self, query):
        """Build plugin URL with parameters"""
        return f"{self.addon_url}?{urlencode(query)}"
    
    def add_directory_item(self, title, url, is_folder=True, info_labels=None, art=None, context_menu=None):
        """Add directory item to Kodi"""
        list_item = xbmcgui.ListItem(label=title)
        
        if info_labels:
            list_item.setInfo('video', info_labels)
        
        if art:
            list_item.setArt(art)
        
        if context_menu:
            list_item.addContextMenuItems(context_menu)
        
        if not is_folder:
            list_item.setProperty('IsPlayable', 'true')
        
        xbmcplugin.addDirectoryItem(
            handle=self.addon_handle,
            url=url,
            listitem=list_item,
            isFolder=is_folder
        )
    
    def login_if_needed(self):
        """Login to Webshare if not already logged in"""
        if self.api.session_token:
            return True
        
        username, password = utils.get_credentials()
        
        if not username or not password:
            utils.show_error(utils.get_string(30052))  # No credentials configured
            self.open_settings()
            return False
        
        if not utils.validate_credentials(username, password):
            utils.show_error(utils.get_string(30053))  # Invalid credentials format
            self.open_settings()
            return False
        
        utils.log(f"Attempting login for user: {username}")
        
        if self.api.login(username, password):
            utils.notify(utils.get_string(30054))  # Login successful
            return True
        else:
            utils.show_error(utils.get_string(30055))  # Login failed
            self.open_settings()
            return False
    
    def open_settings(self):
        """Open addon settings"""
        self.addon.openSettings()
    
    def main_menu(self):
        """Show main menu"""
        utils.log("Showing main menu")
        
        # Search
        self.add_directory_item(
            title=utils.get_string(30060),  # Search
            url=self.build_url({'action': 'search'}),
            is_folder=True
        )
        
        # Browse Movies
        self.add_directory_item(
            title=utils.get_string(30061),  # Movies
            url=self.build_url({'action': 'browse', 'category': 'video'}),
            is_folder=True
        )
        
        # Browse TV Shows
        self.add_directory_item(
            title=utils.get_string(30062),  # TV Shows
            url=self.build_url({'action': 'browse', 'category': 'video'}),
            is_folder=True
        )
        
        # My Queue
        self.add_directory_item(
            title=utils.get_string(30063),  # My Queue
            url=self.build_url({'action': 'queue'}),
            is_folder=True
        )
        
        # Settings
        self.add_directory_item(
            title=utils.get_string(30064),  # Settings
            url=self.build_url({'action': 'settings'}),
            is_folder=False
        )
        
        xbmcplugin.endOfDirectory(self.addon_handle)
    
    def search_menu(self):
        """Show search menu"""
        query = self.params.get('query')
        
        if not query:
            # Show search input
            keyboard = xbmc.Keyboard('', utils.get_string(30065))  # Enter search term
            keyboard.doModal()
            
            if keyboard.isConfirmed():
                query = keyboard.getText()
                if query:
                    self.search_files(query)
            return
        
        self.search_files(query)
    
    def search_files(self, query):
        """Search for files"""
        if not self.login_if_needed():
            return
        
        utils.log(f"Searching for: {query}")
        
        # Show progress dialog
        progress = xbmcgui.DialogProgress()
        progress.create(utils.get_string(30066), utils.get_string(30067))  # Searching, Please wait
        
        try:
            files = self.api.search(query, limit=self.addon.getSettingInt('items_per_page'))
            
            if not files:
                utils.notify(utils.get_string(30068))  # No results found
                return
            
            self.show_files(files)
            
        finally:
            progress.close()
    
    def show_files(self, files):
        """Display list of files"""
        show_adult = self.addon.getSettingBool('show_adult_content')
        show_info = self.addon.getSettingBool('show_file_info')
        
        for file_info in files:
            # Skip adult content if disabled
            if file_info.get('adult', False) and not show_adult:
                continue
            
            # Only show video files
            if not utils.is_video_file(file_info.get('name', ''), file_info.get('type')):
                continue
            
            title = file_info.get('name', 'Unknown')
            file_ident = file_info.get('ident')
            
            if not file_ident:
                continue
            
            # Format title with file info
            if show_info and 'size' in file_info:
                size_str = utils.format_file_size(file_info['size'])
                title = f"{title} ({size_str})"
            
            # Get video info for better display
            video_info = utils.get_video_info(title, file_info)
            
            # Build context menu
            context_menu = [
                (utils.get_string(30070), f"RunPlugin({self.build_url({'action': 'file_info', 'ident': file_ident})})")  # File Info
            ]
            
            # Check if password protected
            if file_info.get('password', False):
                title = f"🔒 {title}"
            
            self.add_directory_item(
                title=title,
                url=self.build_url({'action': 'play', 'ident': file_ident}),
                is_folder=False,
                info_labels=video_info,
                context_menu=context_menu
            )
        
        xbmcplugin.setContent(self.addon_handle, 'movies')
        xbmcplugin.endOfDirectory(self.addon_handle)
    
    def play_file(self):
        """Play video file"""
        file_ident = self.params.get('ident')
        if not file_ident:
            return
        
        if not self.login_if_needed():
            return
        
        utils.log(f"Playing file: {file_ident}")
        
        # Check if file needs password
        file_info = self.api.get_file_info(file_ident)
        if not file_info:
            utils.show_error(utils.get_string(30071))  # File not found
            return
        
        password = None
        if file_info.get('password', False):
            password = utils.ask_for_password()
            if not password:
                return
        
        # Get download link
        download_url = self.api.get_download_link(file_ident, password)
        if not download_url:
            utils.show_error(utils.get_string(30072))  # Cannot get download link
            return
        
        # Create list item and play
        list_item = xbmcgui.ListItem(path=download_url)
        list_item.setInfo('video', utils.get_video_info(file_info.get('name', '')))
        
        xbmcplugin.setResolvedUrl(self.addon_handle, True, list_item)
    
    def run(self):
        """Main plugin entry point"""
        action = self.params.get('action')
        
        if action == 'search':
            self.search_menu()
        elif action == 'play':
            self.play_file()
        elif action == 'settings':
            self.open_settings()
        else:
            self.main_menu()

if __name__ == '__main__':
    plugin = WebsharePlugin()
    plugin.run()
