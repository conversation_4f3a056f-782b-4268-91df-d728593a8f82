# Webshare.cz Kodi Plugin - Developer Guide

## Přehled projektu

Tento projekt implementuje Kodi plugin pro přehrávání filmů a seriálů ze služby Webshare.cz. Plugin využívá oficiální Webshare.cz XML API pro komunikaci se službou.

## Architektura

### Struktura souborů

```
plugin.video.webshare/
├── addon.xml                 # Manifest pluginu
├── addon.py                  # Hlavní entry point
├── LICENSE.txt               # GPL-3.0 licence
├── README.md                 # Uživatelská dokumentace
├── icon.png                  # Ikona pluginu (256x256)
├── fanart.jpg               # Pozadí pluginu (1280x720)
└── resources/
    ├── settings.xml          # Konfigurace nastavení
    ├── lib/
    │   ├── webshare_api.py   # API wrapper
    │   └── utils.py          # Pomocné funkce
    └── language/
        ├── resource.language.en_gb/
        │   └── strings.po    # Anglické texty
        └── resource.language.cs_cz/
            └── strings.po    # České texty
```

### Hlavní komponenty

#### 1. WebshareAPI (webshare_api.py)
- Wrapper pro Webshare.cz XML API
- Implementuje autentifikaci pomocí salt/hash
- Poskytuje metody pro vyhledávání a stahování

#### 2. Utils (utils.py)
- Pomocné funkce pro práci s Kodi
- Detekce video souborů
- Formátování a lokalizace

#### 3. Main Plugin (addon.py)
- Hlavní logika pluginu
- Routing a menu systém
- Integrace s Kodi API

## API Integrace

### Webshare.cz API

Plugin používá následující API endpointy:

- `POST /api/salt/` - Získání salt pro autentifikaci
- `POST /api/login/` - Přihlášení uživatele
- `POST /api/search/` - Vyhledávání souborů
- `POST /api/file_info/` - Informace o souboru
- `POST /api/file_link/` - Direct download link

### Autentifikace

```python
# 1. Získání salt
salt = api.get_salt(username)

# 2. Vytvoření hash
password_hash = sha1(md5(password) + salt)

# 3. Přihlášení
token = api.login(username, password_hash)
```

### Vyhledávání

```python
results = api.search(
    query="film název",
    category="video",
    sort="recent",
    limit=50
)
```

## Kodi Integrace

### Plugin Structure

Plugin je typu `plugin.video` a implementuje:

- **Directory listing** - Procházení menu a výsledků
- **Video playback** - Přehrávání video souborů
- **Settings** - Konfigurace přes Kodi UI
- **Localization** - Podpora češtiny a angličtiny

### Routing

```python
# Hlavní menu
/?action=main

# Vyhledávání
/?action=search&query=text

# Přehrávání
/?action=play&ident=file_id
```

## Vývoj a testování

### Požadavky

- Python 3.6+
- Kodi 19.0+ (Matrix)
- Moduly: requests, xml.etree.ElementTree

### Lokální testování

```bash
# Test API bez Kodi
python test_api.py

# Vytvoření balíčku
python build.py
```

### Debug

Povolte debug logging v nastavení pluginu:
```
Settings → Advanced → Debug logging = True
```

Logy najdete v Kodi log souboru.

## Bezpečnost

### Přihlašovací údaje

- Hesla jsou hashována pomocí MD5+SHA1
- Session token je uložen pouze v paměti
- Žádné údaje nejsou ukládány na disk

### API Limity

- Respektuje rate limiting Webshare.cz
- Implementuje timeout pro požadavky
- Graceful handling chyb

## Rozšíření

### Přidání nových funkcí

1. **Nový API endpoint**:
   ```python
   def new_api_method(self, param):
       return self._make_request('endpoint', {'param': param})
   ```

2. **Nové menu**:
   ```python
   def new_menu(self):
       self.add_directory_item(
           title="New Feature",
           url=self.build_url({'action': 'new_feature'})
       )
   ```

3. **Nové nastavení**:
   ```xml
   <setting id="new_setting" type="boolean" label="30100">
       <default>false</default>
   </setting>
   ```

### Lokalizace

Přidání nového jazyka:

1. Vytvořte složku `resources/language/resource.language.XX_XX/`
2. Přidejte `strings.po` s překlady
3. Aktualizujte `addon.xml` - přidejte jazyk do `<language>`

## Distribuce

### Vytvoření balíčku

```bash
python build.py
```

Vytvoří:
- `plugin.video.webshare-1.0.0.zip` - Instalační balíček
- `plugin.video.webshare-1.0.0/` - Rozbalená struktura
- `INSTALLATION_GUIDE.txt` - Návod k instalaci

### Instalace

1. **Automatická**: Kodi → Add-ons → Install from zip file
2. **Manuální**: Zkopírovat do `~/.kodi/addons/`

## Troubleshooting

### Časté problémy

1. **Plugin se nespustí**
   - Zkontrolujte závislosti (requests, routing)
   - Ověřte strukturu souborů

2. **Nelze se přihlásit**
   - Ověřte přihlašovací údaje
   - Zkontrolujte internetové připojení

3. **Video se nespustí**
   - Zkontrolujte dostupnost souboru
   - Ověřte heslo (pokud je chráněn)

### Debug kroky

1. Povolte debug logging
2. Zkontrolujte Kodi log
3. Spusťte `test_api.py` pro test API
4. Ověřte síťové připojení

## Licence a právní

- **Licence**: GPL-3.0
- **Webshare.cz**: Vyžaduje platný účet
- **Obsah**: Uživatel odpovědný za autorská práva
- **Použití**: Pouze osobní, nekomerční

## Kontakt

Pro hlášení chyb nebo návrhy vytvořte issue na GitHub repozitáři.

---

*Tento plugin není oficiálně podporován společností Webshare.cz*
