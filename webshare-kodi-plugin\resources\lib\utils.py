# -*- coding: utf-8 -*-

import xbmc
import xbmcgui
import xbmcaddon
import os
import re

def get_addon():
    """Get addon instance"""
    return xbmcaddon.Addon()

def get_string(string_id):
    """Get localized string"""
    return get_addon().getLocalizedString(string_id)

def log(message, level=xbmc.LOGDEBUG):
    """Log message"""
    addon = get_addon()
    if addon.getSettingBool('debug_logging') or level >= xbmc.LOGWARNING:
        xbmc.log(f"[Webshare Plugin] {message}", level)

def notify(message, title=None, icon=None, time=5000):
    """Show notification"""
    if not title:
        title = get_string(30000)  # Plugin name
    if not icon:
        icon = get_addon().getAddonInfo('icon')
    
    xbmcgui.Dialog().notification(title, message, icon, time)

def show_error(message, title=None):
    """Show error dialog"""
    if not title:
        title = get_string(30050)  # Error
    
    xbmcgui.Dialog().ok(title, message)
    log(f"Error: {message}", xbmc.LOGERROR)

def ask_for_password(title=None):
    """Ask user for password"""
    if not title:
        title = get_string(30051)  # Enter password
    
    keyboard = xbmc.Keyboard('', title, True)  # True = hidden input
    keyboard.doModal()
    
    if keyboard.isConfirmed():
        return keyboard.getText()
    return None

def format_file_size(size_bytes):
    """Format file size in human readable format"""
    if not size_bytes or not size_bytes.isdigit():
        return "Unknown"
    
    size = int(size_bytes)
    
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size < 1024.0:
            return f"{size:.1f} {unit}"
        size /= 1024.0
    
    return f"{size:.1f} PB"

def clean_filename(filename):
    """Clean filename for display"""
    if not filename:
        return "Unknown"
    
    # Remove file extension for display
    name = os.path.splitext(filename)[0]
    
    # Clean up common patterns
    name = re.sub(r'\[.*?\]', '', name)  # Remove [tags]
    name = re.sub(r'\(.*?\)', '', name)  # Remove (tags)
    name = re.sub(r'\.', ' ', name)      # Replace dots with spaces
    name = re.sub(r'_', ' ', name)       # Replace underscores with spaces
    name = re.sub(r'\s+', ' ', name)     # Multiple spaces to single
    
    return name.strip()

def get_video_info(filename, file_info=None):
    """Extract video information from filename and file info"""
    info = {
        'title': clean_filename(filename),
        'plot': '',
        'year': None,
        'genre': [],
        'mediatype': 'video'
    }
    
    if file_info and 'description' in file_info:
        info['plot'] = file_info['description']
    
    # Try to extract year from filename
    year_match = re.search(r'(19|20)\d{2}', filename)
    if year_match:
        info['year'] = int(year_match.group())
    
    # Detect if it's a TV show episode
    episode_patterns = [
        r'[Ss](\d+)[Ee](\d+)',  # S01E01
        r'(\d+)x(\d+)',         # 1x01
        r'[Ss]eason\s*(\d+).*[Ee]pisode\s*(\d+)',  # Season 1 Episode 1
    ]
    
    for pattern in episode_patterns:
        match = re.search(pattern, filename, re.IGNORECASE)
        if match:
            info['mediatype'] = 'episode'
            info['season'] = int(match.group(1))
            info['episode'] = int(match.group(2))
            break
    
    # Detect movie vs TV show
    if info['mediatype'] == 'video':
        tv_indicators = ['season', 'episode', 'series', 'serial']
        if any(indicator in filename.lower() for indicator in tv_indicators):
            info['mediatype'] = 'tvshow'
        else:
            info['mediatype'] = 'movie'
    
    return info

def is_video_file(filename, file_type=None):
    """Check if file is a video file"""
    if not filename:
        return False
    
    video_extensions = [
        'mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm', 'm4v',
        'mpg', 'mpeg', '3gp', 'ogv', 'ts', 'm2ts', 'mts'
    ]
    
    # Check by file extension
    ext = os.path.splitext(filename)[1].lower().lstrip('.')
    if ext in video_extensions:
        return True
    
    # Check by file type if provided
    if file_type:
        return file_type.lower() in video_extensions
    
    return False

def get_credentials():
    """Get stored credentials"""
    addon = get_addon()
    username = addon.getSetting('username')
    password = addon.getSetting('password')
    
    if not username or not password:
        return None, None
    
    return username, password

def validate_credentials(username, password):
    """Validate credentials format"""
    if not username or not password:
        return False
    
    if len(username) < 3 or len(password) < 6:
        return False
    
    return True
